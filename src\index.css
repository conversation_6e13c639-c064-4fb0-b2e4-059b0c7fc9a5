@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

/* CSS Variables for Dynamic Theming */
:root {
  /* Shadcn/UI Theme Variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.75rem;

  /* Custom Vibrant Variables */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-900: #1e3a8a;

  --secondary-500: #d946ef;
  --secondary-600: #c026d3;

  --accent-500: #10b981;
  --accent-600: #059669;

  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-cosmic: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);

  --glass-light: rgba(255, 255, 255, 0.1);
  --glass-medium: rgba(255, 255, 255, 0.2);
  --glass-heavy: rgba(255, 255, 255, 0.3);

  --shadow-glow: 0 0 50px rgba(59, 130, 246, 0.3);
  --shadow-colorful: 0 0 50px rgba(217, 70, 239, 0.3);
  --shadow-warm: 0 0 50px rgba(251, 146, 60, 0.3);
}

/* Dark mode variables */
.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;

  --glass-light: rgba(0, 0, 0, 0.1);
  --glass-medium: rgba(0, 0, 0, 0.2);
  --glass-heavy: rgba(0, 0, 0, 0.3);
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* Account for fixed header */
  font-size: 16px;
}

/* Enhanced smooth scrolling */
* {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #6c5ce7, #a29bfe);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #fd79a8, #ffeaa7);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #ffffff;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #fd79a8, #6c5ce7, #a29bfe);
  background-size: 800% 800%;
  animation: rainbowShift 8s ease infinite;
  overflow-x: hidden;
  min-height: 100vh;
  position: relative;
}

/* Enhanced rainbow background animation */
@keyframes rainbowShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}

/* Vibrant gradient animations */
@keyframes gradientPulse {
  0%, 100% {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    transform: scale(1);
  }
  50% {
    background: linear-gradient(45deg, #ffeaa7, #fd79a8, #6c5ce7, #a29bfe);
    transform: scale(1.05);
  }
}

/* Floating animation for elements */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(5deg); }
  66% { transform: translateY(-10px) rotate(-5deg); }
}

/* Pulse glow effect */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.5),
                0 0 40px rgba(78, 205, 196, 0.3),
                0 0 60px rgba(69, 183, 209, 0.2);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 234, 167, 0.7),
                0 0 80px rgba(253, 121, 168, 0.5),
                0 0 120px rgba(108, 92, 231, 0.3);
  }
}

/* Animated background with floating particles */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="particle1" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.4"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient><radialGradient id="particle2" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.3"/><stop offset="100%" stop-color="%23667eea" stop-opacity="0"/></radialGradient><radialGradient id="particle3" cx="50%" cy="50%"><stop offset="0%" stop-color="%23f093fb" stop-opacity="0.3"/><stop offset="100%" stop-color="%23f093fb" stop-opacity="0"/></radialGradient></defs><circle cx="100" cy="100" r="60" fill="url(%23particle1)"><animate attributeName="cx" values="100;900;100" dur="25s" repeatCount="indefinite"/><animate attributeName="cy" values="100;200;100" dur="20s" repeatCount="indefinite"/></circle><circle cx="800" cy="200" r="40" fill="url(%23particle2)"><animate attributeName="cy" values="200;800;200" dur="18s" repeatCount="indefinite"/><animate attributeName="cx" values="800;700;800" dur="22s" repeatCount="indefinite"/></circle><circle cx="300" cy="700" r="50" fill="url(%23particle3)"><animate attributeName="cx" values="300;700;300" dur="30s" repeatCount="indefinite"/><animate attributeName="cy" values="700;600;700" dur="25s" repeatCount="indefinite"/></circle><circle cx="600" cy="500" r="35" fill="url(%23particle1)"><animate attributeName="cy" values="500;100;500" dur="20s" repeatCount="indefinite"/><animate attributeName="cx" values="600;500;600" dur="28s" repeatCount="indefinite"/></circle><circle cx="150" cy="400" r="45" fill="url(%23particle2)"><animate attributeName="cx" values="150;850;150" dur="35s" repeatCount="indefinite"/><animate attributeName="cy" values="400;300;400" dur="15s" repeatCount="indefinite"/></circle></svg>');
  pointer-events: none;
  z-index: -1;
  opacity: 0.7;
  animation: particleFloat 40s ease-in-out infinite;
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 0.9; }
}

/* Basic Utility Classes */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-50 { z-index: 50; }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.hidden { display: none; }
.block { display: block; }
.grid { display: grid; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }

.w-full { width: 100%; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.h-full { height: 100%; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.min-h-screen { min-height: 100vh; }

.max-w-7xl { max-width: 80rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-3xl { max-width: 48rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.m-4 { margin: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-16 { margin-bottom: 4rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }
.text-7xl { font-size: 4.5rem; line-height: 1; }
.text-8xl { font-size: 6rem; line-height: 1; }
.text-9xl { font-size: 8rem; line-height: 1; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-black { font-weight: 900; }

.leading-tight { line-height: 1.25; }
.leading-relaxed { line-height: 1.625; }

.text-white { color: #ffffff; }
.text-black { color: #000000; }

.bg-white { background-color: #ffffff; }
.bg-black { background-color: #000000; }

.rounded-full { border-radius: 9999px; }
.rounded-2xl { border-radius: 1rem; }

.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }

.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }

.cursor-pointer { cursor: pointer; }

.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }

.opacity-20 { opacity: 0.2; }
.opacity-60 { opacity: 0.6; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }

/* Responsive Design */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .sm\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .sm\:text-7xl { font-size: 4.5rem; line-height: 1; }
  .sm\:flex-row { flex-direction: row; }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .lg\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .lg\:text-8xl { font-size: 6rem; line-height: 1; }
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
}

@media (min-width: 1280px) {
  .xl\:text-9xl { font-size: 8rem; line-height: 1; }
}

/* Modern Colorful Glass Morphism Components */
.glass-card {
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.2),
    rgba(78, 205, 196, 0.2),
    rgba(69, 183, 209, 0.2)
  );
  backdrop-filter: blur(25px);
  border: 2px solid;
  border-image: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1) 1;
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(255, 107, 107, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: pulseGlow 4s ease-in-out infinite;
}

.glass-card:hover {
  background: linear-gradient(135deg,
    rgba(255, 234, 167, 0.3),
    rgba(253, 121, 168, 0.3),
    rgba(108, 92, 231, 0.3)
  );
  border-image: linear-gradient(45deg, #ffeaa7, #fd79a8, #6c5ce7) 1;
  box-shadow:
    0 20px 60px rgba(253, 121, 168, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-12px) scale(1.02);
}

.glass-button {
  background: var(--glass-light);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 12px 32px;
  color: white;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:hover {
  background: var(--glass-medium);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Vibrant Gradient Buttons with Enhanced Animations */
.gradient-button {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #fd79a8);
  background-size: 300% 300%;
  border: none;
  border-radius: 50px;
  padding: 18px 45px;
  color: white;
  font-weight: 800;
  font-size: 18px;
  text-decoration: none;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 10px 30px rgba(255, 107, 107, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: rainbowShift 2s ease infinite;
}

.gradient-button:hover {
  transform: translateY(-6px) scale(1.08);
  box-shadow:
    0 25px 50px rgba(255, 107, 107, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation-duration: 1s;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s;
}

.gradient-button:hover::before {
  left: 100%;
}

.gradient-button-secondary {
  background: var(--gradient-secondary);
  box-shadow: var(--shadow-colorful);
}

.gradient-button-accent {
  background: var(--gradient-accent);
  box-shadow: var(--shadow-warm);
}

/* Modern Colorful Card Designs */
.modern-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.8)
  );
  border-radius: 32px;
  padding: 40px;
  box-shadow:
    0 20px 60px rgba(255, 107, 107, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #fd79a8);
  background-size: 300% 300%;
  animation: rainbowShift 3s ease infinite;
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.modern-card:hover::before {
  transform: scaleX(1);
}

.modern-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow:
    0 35px 90px rgba(255, 107, 107, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.9)
  );
  border: 2px solid rgba(255, 107, 107, 0.2);
}

/* Floating Action Cards */
.floating-card {
  background: var(--glass-medium);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 28px;
  padding: 32px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: floatAnimation 6s ease-in-out infinite;
}

@keyframes floatAnimation {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-8px) rotate(0.5deg); }
  66% { transform: translateY(-4px) rotate(-0.5deg); }
}

.floating-card:hover {
  transform: translateY(-16px) scale(1.02);
  background: var(--glass-heavy);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
  animation-play-state: paused;
}

/* Neon Glow Effects */
.neon-glow {
  position: relative;
  color: #fff;
  text-shadow: 0 0 10px currentColor;
}

.neon-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  filter: blur(15px);
  opacity: 0.7;
  z-index: -1;
}

/* Morphing Shapes */
.morphing-blob {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: morphing 8s ease-in-out infinite;
}

@keyframes morphing {
  0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
  50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
  100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
}

/* Advanced Vibrant Text Animations */
.gradient-text {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #fd79a8, #6c5ce7, #a29bfe);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: rainbowShift 3s ease infinite;
  text-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
}

.rainbow-text {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #fd79a8, #6c5ce7, #a29bfe, #00b894, #e17055);
  background-size: 500% 500%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: rainbowShift 2s ease infinite;
  filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.3));
}

.neon-text {
  color: #fff;
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px #ff6b6b,
    0 0 35px #ff6b6b,
    0 0 40px #ff6b6b;
  animation: neonFlicker 2s ease-in-out infinite alternate;
}

@keyframes neonFlicker {
  0%, 100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px #ff6b6b,
      0 0 35px #ff6b6b,
      0 0 40px #ff6b6b;
  }
  50% {
    text-shadow:
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px #4ecdc4,
      0 0 18px #4ecdc4,
      0 0 25px #4ecdc4;
  }
}

@keyframes rainbowShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.typing-animation {
  overflow: hidden;
  border-right: 3px solid;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: currentColor; }
}

/* Parallax Scrolling Effects */
.parallax-slow {
  transform: translateZ(0);
  will-change: transform;
}

.parallax-medium {
  transform: translateZ(0);
  will-change: transform;
}

.parallax-fast {
  transform: translateZ(0);
  will-change: transform;
}

/* 3D Transform Effects */
.card-3d {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.card-3d-inner {
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.card-3d:hover .card-3d-inner {
  transform: rotateY(180deg);
}

.card-3d-front,
.card-3d-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 24px;
}

.card-3d-back {
  transform: rotateY(180deg);
}

/* Magnetic Button Effect */
.magnetic-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.magnetic-button:hover {
  transform: scale(1.05);
}

/* Liquid Button Effect */
.liquid-button {
  position: relative;
  background: var(--gradient-primary);
  border: none;
  border-radius: 50px;
  padding: 16px 40px;
  color: white;
  font-weight: 600;
  overflow: hidden;
  transition: all 0.3s ease;
}

.liquid-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.liquid-button:hover::before {
  width: 300px;
  height: 300px;
}

/* Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* Loading Animations */
.pulse-loader {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.bounce-loader {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8, 0, 1, 1); }
  50% { transform: none; animation-timing-function: cubic-bezier(0, 0, 0.2, 1); }
}

/* Spinner Animations */
.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #fff;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Layout utilities */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-2xl { max-width: 42rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* Display utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* Positioning */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.top-0 { top: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-10 { z-index: 10; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* Sizing */
.h-4 { height: 1rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-64 { height: 16rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-auto { width: auto; }

/* Spacing */
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-12 { padding: 3rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }

.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.mt-16 { margin-top: 4rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mb-20 { margin-bottom: 5rem; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }
.text-7xl { font-size: 4.5rem; line-height: 1; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }

/* Colors */
.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-50 { color: #f9fafb; }
.text-gray-100 { color: #f3f4f6; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-blue-500 { color: #3b82f6; }
.text-blue-600 { color: #2563eb; }
.text-purple-600 { color: #9333ea; }

.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-purple-50 { background-color: #faf5ff; }
.bg-purple-600 { background-color: #9333ea; }

/* Border and rounded */
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

/* Transitions */
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* Transform */
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Hover effects */
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:text-blue-600:hover { color: #2563eb; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Visibility */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.inline-flex { display: inline-flex; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Gradient backgrounds */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }

/* Vibrant gradient system */
.bg-primary-gradient {
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%);
  background-size: 300% 300%;
  animation: gradientFlow 8s ease infinite;
}

.text-primary-gradient {
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%);
  background-size: 300% 300%;
  animation: gradientFlow 8s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Hero section gradient */
.hero-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 400% 400%;
  animation: gradientShift 12s ease infinite;
  position: relative;
  overflow: hidden;
}

/* Vibrant card gradients */
.card-gradient-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  animation: cardFloat 6s ease-in-out infinite;
}

.card-gradient-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 20px 40px rgba(240, 147, 251, 0.3);
  animation: cardFloat 6s ease-in-out infinite 1s;
}

.card-gradient-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 20px 40px rgba(79, 172, 254, 0.3);
  animation: cardFloat 6s ease-in-out infinite 2s;
}

.card-gradient-4 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 20px 40px rgba(67, 233, 123, 0.3);
  animation: cardFloat 6s ease-in-out infinite 3s;
}

.card-gradient-5 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  box-shadow: 0 20px 40px rgba(250, 112, 154, 0.3);
  animation: cardFloat 6s ease-in-out infinite 4s;
}

.card-gradient-6 {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 20px 40px rgba(168, 237, 234, 0.3);
  animation: cardFloat 6s ease-in-out infinite 5s;
}

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

/* Vibrant animated button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-weight: 700;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%);
  background-size: 300% 300%;
  animation: gradientFlow 4s ease infinite;
  color: white;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
  border: 2px solid transparent;
}

.btn-primary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
  animation-duration: 2s;
}

.btn-secondary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  border: 2px solid transparent;
}

.btn-secondary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 3px solid;
  border-image: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7) 1;
  backdrop-filter: blur(20px);
  position: relative;
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.3);
}

.btn-glow {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow: 0 0 30px rgba(240, 147, 251, 0.6);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { box-shadow: 0 0 30px rgba(240, 147, 251, 0.6); }
  to { box-shadow: 0 0 50px rgba(240, 147, 251, 0.8), 0 0 80px rgba(245, 87, 108, 0.4); }
}

.btn-glow:hover {
  transform: translateY(-4px) scale(1.05);
  animation-duration: 1s;
}

/* Vibrant animated card styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2rem;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.card:hover::before {
  opacity: 0.1;
}

.card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 107, 107, 0.3);
}

.card-gradient {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.card-glow {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2rem;
  padding: 2.5rem;
  position: relative;
  overflow: hidden;
  animation: cardGlow 4s ease-in-out infinite;
}

@keyframes cardGlow {
  0%, 100% {
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);
  }
  25% {
    box-shadow: 0 20px 40px rgba(76, 201, 196, 0.2);
  }
  50% {
    box-shadow: 0 20px 40px rgba(69, 183, 209, 0.2);
  }
  75% {
    box-shadow: 0 20px 40px rgba(150, 206, 180, 0.2);
  }
}

/* Vibrant navigation styles */
.nav-link {
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
  transition: left 0.3s ease;
  z-index: -1;
  border-radius: 2rem;
}

.nav-link:hover::before {
  left: 0;
}

.nav-link:hover {
  color: #ffffff;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

/* Form styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
  resize: vertical;
  min-height: 120px;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Vibrant hero section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #f5576c 60%, #4facfe 80%, #00f2fe 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.3"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient><radialGradient id="b" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ff6b6b" stop-opacity="0.2"/><stop offset="100%" stop-color="%23ff6b6b" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"><animate attributeName="cx" values="200;800;200" dur="20s" repeatCount="indefinite"/></circle><circle cx="800" cy="300" r="150" fill="url(%23b)"><animate attributeName="cy" values="300;700;300" dur="25s" repeatCount="indefinite"/></circle><circle cx="400" cy="700" r="120" fill="url(%23a)"><animate attributeName="cx" values="400;600;400" dur="18s" repeatCount="indefinite"/></circle><circle cx="600" cy="100" r="80" fill="url(%23b)"><animate attributeName="cy" values="100;900;100" dur="22s" repeatCount="indefinite"/></circle></svg>');
  opacity: 0.6;
  animation: float 20s ease-in-out infinite;
}

.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(76, 201, 196, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(69, 183, 209, 0.3) 0%, transparent 50%);
  animation: colorShift 12s ease infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

@keyframes colorShift {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.8; }
}

.pricing-card {
  background: white;
  border-radius: 1.5rem;
  padding: 2.5rem 2rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.pricing-card.featured {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.pricing-card.featured::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #8b5cf6 100%);
}

.section-title {
  font-size: 3rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile menu styles */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-content {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin: 1rem;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Scroll down indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  text-align: center;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
  40% { transform: translateX(-50%) translateY(-10px); }
  60% { transform: translateX(-50%) translateY(-5px); }
}

/* Star rating */
.star-rating {
  display: flex;
  gap: 0.25rem;
}

.star {
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.star.filled {
  color: #fbbf24;
}

.star.empty {
  color: #d1d5db;
}

.star:hover {
  transform: scale(1.1);
}

/* Loading spinner */
.spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (min-width: 640px) {
  .sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .sm\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .sm\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .sm\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .sm\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .sm\:text-5xl { font-size: 3rem; line-height: 1; }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:py-8 { padding-top: 2rem; padding-bottom: 2rem; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:gap-8 { gap: 2rem; }
}

@media (min-width: 768px) {
  .md\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .md\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .md\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .md\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .md\:text-5xl { font-size: 3rem; line-height: 1; }
  .md\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .md\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .md\:py-12 { padding-top: 3rem; padding-bottom: 3rem; }
  .md\:py-16 { padding-top: 4rem; padding-bottom: 4rem; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:gap-8 { gap: 2rem; }
  .md\:gap-12 { gap: 3rem; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:block { display: block; }
}

@media (min-width: 1024px) {
  .lg\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .lg\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .lg\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .lg\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .lg\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .lg\:text-5xl { font-size: 3rem; line-height: 1; }
  .lg\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .lg\:text-7xl { font-size: 4.5rem; line-height: 1; }
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:py-16 { padding-top: 4rem; padding-bottom: 4rem; }
  .lg\:py-20 { padding-top: 5rem; padding-bottom: 5rem; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:gap-8 { gap: 2rem; }
  .lg\:gap-12 { gap: 3rem; }
  .lg\:flex { display: flex; }
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:inline-flex { display: inline-flex; }
  .lg\:h-20 { height: 5rem; }
  .lg\:space-x-8 > * + * { margin-left: 2rem; }
}

@media (min-width: 1280px) {
  .xl\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .xl\:text-7xl { font-size: 4.5rem; line-height: 1; }
  .xl\:text-8xl { font-size: 6rem; line-height: 1; }
  .xl\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .xl\:py-20 { padding-top: 5rem; padding-bottom: 5rem; }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .xl\:gap-12 { gap: 3rem; }
}

/* Vibrant custom animations */
.fade-in {
  animation: fadeInVibrant 0.8s ease-in-out;
}

@keyframes fadeInVibrant {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    filter: blur(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

.slide-in-left {
  animation: slideInLeftVibrant 0.8s ease-out;
}

@keyframes slideInLeftVibrant {
  from {
    opacity: 0;
    transform: translateX(-80px) rotate(-5deg);
    filter: blur(3px);
  }
  to {
    opacity: 1;
    transform: translateX(0) rotate(0deg);
    filter: blur(0px);
  }
}

.slide-in-right {
  animation: slideInRightVibrant 0.8s ease-out;
}

@keyframes slideInRightVibrant {
  from {
    opacity: 0;
    transform: translateX(80px) rotate(5deg);
    filter: blur(3px);
  }
  to {
    opacity: 1;
    transform: translateX(0) rotate(0deg);
    filter: blur(0px);
  }
}

/* Pulsing glow effect */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 107, 107, 0.8), 0 0 60px rgba(76, 201, 196, 0.4);
    transform: scale(1.02);
  }
}

/* Rainbow text effect */
.rainbow-text {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #fd79a8);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: rainbowShift 3s ease infinite;
}

@keyframes rainbowShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Floating animation */
.float {
  animation: floatAnimation 6s ease-in-out infinite;
}

@keyframes floatAnimation {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

/* Bounce in animation */
.bounce-in {
  animation: bounceIn 1s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-10deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) rotate(2deg);
  }
  70% {
    transform: scale(0.9) rotate(-1deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Utility classes for better spacing */
.section-padding {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

@media (min-width: 768px) {
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}

/* Vibrant header styles */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border-bottom: 2px solid rgba(255, 107, 107, 0.3);
}

/* Vibrant logo styles */
.logo {
  font-size: 1.8rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%);
  background-size: 300% 300%;
  animation: gradientFlow 4s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  text-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
  animation-duration: 2s;
}

/* Vibrant category section styles */
.category-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
  position: relative;
  overflow: hidden;
}

.category-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.category-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2rem;
  padding: 2.5rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  height: 100%;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.category-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.5s;
  opacity: 0;
}

.category-card:hover::before {
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

.category-card:hover {
  transform: translateY(-15px) scale(1.05);
  box-shadow: 0 35px 70px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 107, 107, 0.5);
}

.category-icon {
  width: 5rem;
  height: 5rem;
  border-radius: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  color: white;
  position: relative;
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.category-icon.web {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}
.category-icon.app {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 15px 30px rgba(240, 147, 251, 0.4);
}
.category-icon.ai {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 15px 30px rgba(79, 172, 254, 0.4);
}
.category-icon.devops {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 15px 30px rgba(67, 233, 123, 0.4);
}

/* Project card styles */
.project-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.project-image {
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
}

.project-content {
  padding: 1.5rem;
}

/* Vibrant arts section styles */
.arts-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #f5576c 60%, #4facfe 80%, #00f2fe 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
  color: white;
  position: relative;
  overflow: hidden;
}

.arts-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(255, 107, 107, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(76, 201, 196, 0.3) 0%, transparent 50%);
  animation: colorShift 15s ease infinite;
}

.arts-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 2rem;
  padding: 3rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.arts-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s;
  opacity: 0;
}

.arts-card:hover::before {
  animation: shimmer 1.5s ease-in-out;
}

.arts-card:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-15px) scale(1.05);
  box-shadow: 0 35px 70px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.arts-icon {
  width: 5rem;
  height: 5rem;
  border-radius: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 2rem;
  box-shadow: 0 15px 30px rgba(255, 255, 255, 0.2);
  animation: iconFloat 3s ease-in-out infinite;
}

/* Vibrant contact section styles */
.contact-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 400% 400%;
  animation: gradientShift 25s ease infinite;
  position: relative;
  overflow: hidden;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.contact-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 2rem;
  padding: 3rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(30px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(76, 201, 196, 0.05) 100%);
  z-index: -1;
}

/* Footer styles */
.footer {
  background: #1f2937;
  color: white;
}

.footer-section h3 {
  color: white;
  margin-bottom: 1rem;
  font-weight: 600;
}

.footer-link {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #3b82f6;
}

/* Feedback section styles */
.feedback-section {
  background: white;
}

.testimonial-card {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 2rem;
  border-left: 4px solid #3b82f6;
  margin-bottom: 2rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  margin-top: 1rem;
}

.testimonial-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 1rem;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
