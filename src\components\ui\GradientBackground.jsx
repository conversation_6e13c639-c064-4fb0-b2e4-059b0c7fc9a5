import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

const GradientBackground = ({ 
  variant = "cosmic", 
  animated = true, 
  className = "",
  children,
  ...props 
}) => {
  const canvasRef = useRef(null);

  const gradientVariants = {
    cosmic: {
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)",
      colors: ["#667eea", "#764ba2", "#f093fb", "#f5576c", "#4facfe"]
    },
    aurora: {
      background: "linear-gradient(135deg, #00c9ff 0%, #92fe9d 25%, #00c9ff 50%, #92fe9d 75%, #00c9ff 100%)",
      colors: ["#00c9ff", "#92fe9d", "#00c9ff", "#92fe9d", "#00c9ff"]
    },
    sunset: {
      background: "linear-gradient(135deg, #fa709a 0%, #fee140 25%, #fa709a 50%, #fee140 75%, #fa709a 100%)",
      colors: ["#fa709a", "#fee140", "#fa709a", "#fee140", "#fa709a"]
    },
    ocean: {
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 25%, #667eea 50%, #764ba2 75%, #667eea 100%)",
      colors: ["#667eea", "#764ba2", "#667eea", "#764ba2", "#667eea"]
    },
    vibrant: {
      background: "linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 20%, #45b7d1 40%, #96ceb4 60%, #ffeaa7 80%, #fd79a8 100%)",
      colors: ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#ffeaa7", "#fd79a8"]
    }
  };

  useEffect(() => {
    if (!animated || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationId;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const particles = [];
    const particleCount = 50;
    const colors = gradientVariants[variant].colors;

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: Math.random() * 3 + 1,
        color: colors[Math.floor(Math.random() * colors.length)],
        velocity: {
          x: (Math.random() - 0.5) * 0.5,
          y: (Math.random() - 0.5) * 0.5
        },
        opacity: Math.random() * 0.5 + 0.2
      });
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach((particle, index) => {
        // Update position
        particle.x += particle.velocity.x;
        particle.y += particle.velocity.y;

        // Bounce off edges
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.velocity.x *= -1;
        }
        if (particle.y < 0 || particle.y > canvas.height) {
          particle.velocity.y *= -1;
        }

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
        ctx.fillStyle = particle.color + Math.floor(particle.opacity * 255).toString(16).padStart(2, '0');
        ctx.fill();

        // Draw connections
        particles.forEach((otherParticle, otherIndex) => {
          if (index !== otherIndex) {
            const distance = Math.sqrt(
              Math.pow(particle.x - otherParticle.x, 2) + 
              Math.pow(particle.y - otherParticle.y, 2)
            );

            if (distance < 100) {
              ctx.beginPath();
              ctx.moveTo(particle.x, particle.y);
              ctx.lineTo(otherParticle.x, otherParticle.y);
              ctx.strokeStyle = particle.color + '20';
              ctx.lineWidth = 1;
              ctx.stroke();
            }
          }
        });
      });

      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [variant, animated]);

  return (
    <div className={`relative overflow-hidden ${className}`} {...props}>
      {/* Static gradient background */}
      <div 
        className="absolute inset-0"
        style={{
          background: gradientVariants[variant].background,
          backgroundSize: animated ? "400% 400%" : "100% 100%",
          animation: animated ? "gradientShift 15s ease infinite" : "none"
        }}
      />

      {/* Animated canvas overlay */}
      {animated && (
        <canvas
          ref={canvasRef}
          className="absolute inset-0 opacity-30"
          style={{ mixBlendMode: 'overlay' }}
        />
      )}

      {/* Overlay effects */}
      <div className="absolute inset-0">
        {/* Radial gradients for depth */}
        <div 
          className="absolute inset-0 opacity-40"
          style={{
            background: `
              radial-gradient(circle at 20% 80%, ${gradientVariants[variant].colors[0]}40 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, ${gradientVariants[variant].colors[1]}40 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, ${gradientVariants[variant].colors[2]}30 0%, transparent 50%)
            `
          }}
        />

        {/* Animated floating orbs */}
        {animated && (
          <>
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full opacity-20 blur-xl"
                style={{
                  width: `${Math.random() * 200 + 100}px`,
                  height: `${Math.random() * 200 + 100}px`,
                  background: gradientVariants[variant].colors[i % gradientVariants[variant].colors.length],
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  x: [0, Math.random() * 100 - 50, 0],
                  y: [0, Math.random() * 100 - 50, 0],
                  scale: [1, Math.random() * 0.5 + 0.8, 1],
                  opacity: [0.2, 0.4, 0.2]
                }}
                transition={{
                  duration: Math.random() * 10 + 10,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            ))}
          </>
        )}
      </div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

// Glassmorphism container component
export const GlassContainer = ({ 
  children, 
  className = "",
  blur = "xl",
  opacity = 10,
  border = true,
  ...props 
}) => {
  const blurClasses = {
    sm: "backdrop-blur-sm",
    md: "backdrop-blur-md",
    lg: "backdrop-blur-lg",
    xl: "backdrop-blur-xl",
    "2xl": "backdrop-blur-2xl"
  };

  return (
    <div 
      className={`
        ${blurClasses[blur]} 
        bg-white/${opacity} 
        ${border ? 'border border-white/20' : ''} 
        rounded-2xl 
        shadow-xl 
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
};

// Morphing blob component
export const MorphingBlob = ({ 
  size = 200, 
  color = "#ff6b6b", 
  className = "",
  ...props 
}) => {
  return (
    <motion.div
      className={`absolute rounded-full opacity-20 blur-2xl ${className}`}
      style={{
        width: size,
        height: size,
        background: `linear-gradient(45deg, ${color}, ${color}80)`
      }}
      animate={{
        borderRadius: [
          "60% 40% 30% 70% / 60% 30% 70% 40%",
          "30% 60% 70% 40% / 50% 60% 30% 60%",
          "60% 40% 30% 70% / 60% 30% 70% 40%"
        ],
        scale: [1, 1.2, 1],
        rotate: [0, 180, 360]
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      {...props}
    />
  );
};

export default GradientBackground;
