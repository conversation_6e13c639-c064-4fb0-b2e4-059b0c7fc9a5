import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export const useScrollAnimations = () => {
  const initialized = useRef(false);

  useEffect(() => {
    if (initialized.current) return;
    initialized.current = true;

    // Parallax backgrounds
    gsap.utils.toArray('.parallax-bg').forEach((bg, i) => {
      gsap.fromTo(bg, 
        { yPercent: -50 },
        {
          yPercent: 50,
          ease: "none",
          scrollTrigger: {
            trigger: bg,
            start: "top bottom",
            end: "bottom top",
            scrub: true
          }
        }
      );
    });

    // Reveal animations
    gsap.utils.toArray('.reveal-up').forEach((element, i) => {
      gsap.fromTo(element,
        { y: 100, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    // Scale animations
    gsap.utils.toArray('.scale-in').forEach((element, i) => {
      gsap.fromTo(element,
        { scale: 0.8, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          duration: 1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    // Rotate animations
    gsap.utils.toArray('.rotate-in').forEach((element, i) => {
      gsap.fromTo(element,
        { rotation: -10, opacity: 0, scale: 0.9 },
        {
          rotation: 0,
          opacity: 1,
          scale: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    // Slide animations
    gsap.utils.toArray('.slide-left').forEach((element, i) => {
      gsap.fromTo(element,
        { x: -100, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    gsap.utils.toArray('.slide-right').forEach((element, i) => {
      gsap.fromTo(element,
        { x: 100, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    // Stagger animations
    gsap.utils.toArray('.stagger-container').forEach((container, i) => {
      const children = container.children;
      gsap.fromTo(children,
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: container,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    // Text reveal animations
    gsap.utils.toArray('.text-reveal').forEach((element, i) => {
      const text = element.textContent;
      element.innerHTML = text.split('').map(char => 
        `<span style="display: inline-block; opacity: 0; transform: translateY(50px);">${char === ' ' ? '&nbsp;' : char}</span>`
      ).join('');

      gsap.to(element.children, {
        opacity: 1,
        y: 0,
        duration: 0.05,
        stagger: 0.02,
        ease: "power3.out",
        scrollTrigger: {
          trigger: element,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });
    });

    // Morphing shapes
    gsap.utils.toArray('.morph-shape').forEach((element, i) => {
      gsap.fromTo(element,
        { 
          borderRadius: "50%",
          scale: 0,
          rotation: 180
        },
        {
          borderRadius: "0%",
          scale: 1,
          rotation: 0,
          duration: 1.5,
          ease: "elastic.out(1, 0.3)",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    // Floating animations
    gsap.utils.toArray('.float-element').forEach((element, i) => {
      gsap.to(element, {
        y: -20,
        duration: 2,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
        delay: i * 0.2
      });
    });

    // Progress bar animations
    gsap.utils.toArray('.progress-bar').forEach((bar, i) => {
      const progress = bar.dataset.progress || 100;
      gsap.fromTo(bar,
        { width: "0%" },
        {
          width: `${progress}%`,
          duration: 2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: bar,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return {
    refresh: () => ScrollTrigger.refresh(),
    kill: () => ScrollTrigger.getAll().forEach(trigger => trigger.kill())
  };
};

export const useParallax = (ref, speed = 0.5) => {
  useEffect(() => {
    if (!ref.current) return;

    const element = ref.current;
    
    gsap.fromTo(element,
      { yPercent: -50 * speed },
      {
        yPercent: 50 * speed,
        ease: "none",
        scrollTrigger: {
          trigger: element,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill();
        }
      });
    };
  }, [ref, speed]);
};

export const useRevealAnimation = (ref, options = {}) => {
  const {
    direction = 'up',
    distance = 100,
    duration = 1,
    delay = 0,
    ease = "power3.out"
  } = options;

  useEffect(() => {
    if (!ref.current) return;

    const element = ref.current;
    const fromVars = { opacity: 0 };
    const toVars = { opacity: 1, duration, delay, ease };

    switch (direction) {
      case 'up':
        fromVars.y = distance;
        toVars.y = 0;
        break;
      case 'down':
        fromVars.y = -distance;
        toVars.y = 0;
        break;
      case 'left':
        fromVars.x = -distance;
        toVars.x = 0;
        break;
      case 'right':
        fromVars.x = distance;
        toVars.x = 0;
        break;
      case 'scale':
        fromVars.scale = 0.8;
        toVars.scale = 1;
        break;
    }

    gsap.fromTo(element, fromVars, {
      ...toVars,
      scrollTrigger: {
        trigger: element,
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill();
        }
      });
    };
  }, [ref, direction, distance, duration, delay, ease]);
};
