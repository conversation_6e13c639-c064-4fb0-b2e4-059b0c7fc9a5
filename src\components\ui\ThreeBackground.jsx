import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Points, PointMaterial, Sphere } from '@react-three/drei';
import * as random from 'maath/random/dist/maath-random.esm';

function Stars(props) {
  const ref = useRef();
  const [sphere] = useMemo(() => [random.inSphere(new Float32Array(5000), { radius: 1.5 })], []);

  useFrame((state, delta) => {
    ref.current.rotation.x -= delta / 10;
    ref.current.rotation.y -= delta / 15;
  });

  return (
    <group rotation={[0, 0, Math.PI / 4]}>
      <Points ref={ref} positions={sphere} stride={3} frustumCulled={false} {...props}>
        <PointMaterial
          transparent
          color="#ffa0e0"
          size={0.005}
          sizeAttenuation={true}
          depthWrite={false}
        />
      </Points>
    </group>
  );
}

function FloatingGeometry({ position, color, speed = 1 }) {
  const meshRef = useRef();

  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    meshRef.current.rotation.x = time * speed * 0.5;
    meshRef.current.rotation.y = time * speed * 0.3;
    meshRef.current.position.y = position[1] + Math.sin(time * speed) * 0.1;
  });

  return (
    <mesh ref={meshRef} position={position}>
      <icosahedronGeometry args={[0.1, 1]} />
      <meshStandardMaterial color={color} transparent opacity={0.6} />
    </mesh>
  );
}

function AnimatedSphere({ position, color, scale = 1 }) {
  const meshRef = useRef();

  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    meshRef.current.rotation.x = time * 0.2;
    meshRef.current.rotation.y = time * 0.3;
    meshRef.current.scale.setScalar(scale + Math.sin(time * 2) * 0.1);
  });

  return (
    <mesh ref={meshRef} position={position}>
      <sphereGeometry args={[0.05, 32, 32]} />
      <meshStandardMaterial color={color} transparent opacity={0.8} />
    </mesh>
  );
}

export default function ThreeBackground({ className = "" }) {
  const geometries = useMemo(() => {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#fd79a8', '#6c5ce7', '#a29bfe'];
    return Array.from({ length: 20 }, (_, i) => ({
      position: [
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 4
      ],
      color: colors[i % colors.length],
      speed: Math.random() * 2 + 0.5
    }));
  }, []);

  const spheres = useMemo(() => {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#fd79a8'];
    return Array.from({ length: 15 }, (_, i) => ({
      position: [
        (Math.random() - 0.5) * 6,
        (Math.random() - 0.5) * 6,
        (Math.random() - 0.5) * 6
      ],
      color: colors[i % colors.length],
      scale: Math.random() * 0.5 + 0.5
    }));
  }, []);

  return (
    <div className={`fixed inset-0 -z-10 ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 1] }}
        style={{ background: 'transparent' }}
      >
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} />
        <Stars />
        
        {geometries.map((geo, index) => (
          <FloatingGeometry
            key={index}
            position={geo.position}
            color={geo.color}
            speed={geo.speed}
          />
        ))}
        
        {spheres.map((sphere, index) => (
          <AnimatedSphere
            key={index}
            position={sphere.position}
            color={sphere.color}
            scale={sphere.scale}
          />
        ))}
      </Canvas>
    </div>
  );
}
