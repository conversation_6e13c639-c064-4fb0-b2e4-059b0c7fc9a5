import React, { useState } from 'react';
import { motion } from 'framer-motion';

const PricingSection = () => {
  const [selectedPlan, setSelectedPlan] = useState(1);

  const plans = [
    {
      name: 'Starter',
      price: '$999',
      period: 'per project',
      description: 'Perfect for small businesses and startups',
      features: [
        'Basic Website (up to 5 pages)',
        'Responsive Design',
        'Basic SEO Setup',
        'Contact Form Integration',
        '30 Days Support',
        'Basic Analytics Setup',
        'Social Media Integration'
      ],
      popular: false,
      color: 'from-pink-500 to-rose-500'
    },
    {
      name: 'Growth',
      price: '$2,499',
      period: 'per project',
      description: 'Ideal for growing businesses',
      features: [
        'Advanced Website (up to 15 pages)',
        'Custom Design & Animations',
        'Advanced SEO & Performance',
        'CMS Integration',
        'E-commerce Functionality',
        '90 Days Support',
        'Advanced Analytics',
        'API Integrations',
        'Mobile App (Basic)',
        'Social Media Strategy'
      ],
      popular: true,
      color: 'from-purple-500 to-indigo-500'
    },
    {
      name: 'Enterprise',
      price: '$5,999',
      period: 'per project',
      description: 'For large-scale applications',
      features: [
        'Custom Web Application',
        'Advanced Mobile App',
        'AI/ML Integration',
        'Custom Backend Development',
        'DevOps & Cloud Setup',
        'Advanced Security',
        '6 Months Support',
        'Performance Optimization',
        'Third-party Integrations',
        'Custom Analytics Dashboard',
        'Dedicated Project Manager',
        'Priority Support'
      ],
      popular: false,
      color: 'from-blue-500 to-cyan-500'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const scrollToContact = () => {
    document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="pricing" className="relative py-20 lg:py-32 overflow-hidden">
      {/* Vibrant Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100"></div>

        {/* Animated background shapes */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full opacity-15"
            style={{
              width: `${Math.random() * 400 + 200}px`,
              height: `${Math.random() * 400 + 200}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              background: `linear-gradient(135deg, ${
                ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#fd79a8'][i]
              }, ${
                ['#ffeaa7', '#fd79a8', '#6c5ce7', '#a29bfe', '#00b894', '#e17055'][i]
              })`
            }}
            animate={{
              scale: [1, 1.3, 1],
              rotate: [0, 180, 360],
              x: [0, Math.random() * 100 - 50, 0],
              y: [0, Math.random() * 100 - 50, 0],
            }}
            transition={{
              duration: Math.random() * 25 + 20,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-black mb-4 rainbow-text">
            Transparent Pricing
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Choose the perfect plan for your project. All plans include our commitment 
            to quality and timely delivery.
          </p>
        </motion.div>

        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ 
                y: plan.popular ? -5 : -10,
                scale: plan.popular ? 1.02 : 1.05
              }}
              onClick={() => setSelectedPlan(index)}
              className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer ${
                plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''
              } ${selectedPlan === index ? 'ring-2 ring-accent-500' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-primary-600 to-accent-600 text-white px-6 py-2 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="p-8">
                <div className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-xl flex items-center justify-center mb-6`}>
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>

                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>

                <p className="text-gray-600 mb-6">
                  {plan.description}
                </p>

                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900">
                    {plan.price}
                  </span>
                  <span className="text-gray-600 ml-2">
                    {plan.period}
                  </span>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-600">
                      <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={scrollToContact}
                  className={`w-full py-3 font-semibold rounded-lg transition-all duration-200 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-primary-600 to-accent-600 text-white hover:from-primary-700 hover:to-accent-700 shadow-lg hover:shadow-xl'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  }`}
                >
                  Get Started
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Information */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Need Something Custom?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Every project is unique. If our standard plans don't fit your needs, 
              we'd love to create a custom solution tailored specifically for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={scrollToContact}
                className="px-8 py-3 bg-gradient-to-r from-primary-600 to-accent-600 text-white font-semibold rounded-lg hover:from-primary-700 hover:to-accent-700 transition-all duration-200 shadow-lg"
              >
                Request Custom Quote
              </motion.button>
              <motion.a
                href="https://calendly.com/akrix-ai/akrix-freelancing-meeting"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-3 border-2 border-primary-600 text-primary-600 font-semibold rounded-lg hover:bg-primary-600 hover:text-white transition-all duration-200"
              >
                Schedule Consultation
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
      </div>
    </section>
  );
};

export default PricingSection;
