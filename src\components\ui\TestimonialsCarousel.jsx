import React, { useRef, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectCards, Pagination, Navigation, Autoplay, EffectCoverflow } from 'swiper/modules';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from './card';
import { Badge } from './badge';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/effect-cards';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

const TestimonialsCarousel = ({ testimonials, effect = "cards" }) => {
  const swiperRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, rotateY: -15 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      rotateY: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      rotateY: 15,
      transition: {
        duration: 0.3
      }
    }
  };

  const StarRating = ({ rating }) => {
    return (
      <div className="flex space-x-1">
        {[...Array(5)].map((_, i) => (
          <motion.span
            key={i}
            className={`text-lg ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: i * 0.1, duration: 0.3 }}
            whileHover={{ scale: 1.2 }}
          >
            ⭐
          </motion.span>
        ))}
      </div>
    );
  };

  const TestimonialCard = ({ testimonial, isActive }) => (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      className="h-full"
    >
      <Card className="h-full bg-white/10 backdrop-blur-xl border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden relative">
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        {/* Quote icon */}
        <div className="absolute top-4 right-4 text-4xl text-white/20 group-hover:text-white/40 transition-colors duration-300">
          "
        </div>
        
        <CardContent className="p-8 relative z-10 h-full flex flex-col justify-between">
          <div>
            {/* Rating */}
            <div className="mb-4">
              <StarRating rating={testimonial.rating} />
            </div>
            
            {/* Testimonial text */}
            <motion.p 
              className="text-white/90 text-lg leading-relaxed mb-6 italic"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              "{testimonial.text}"
            </motion.p>
          </div>
          
          {/* Author info */}
          <motion.div 
            className="flex items-center space-x-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <motion.div 
              className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg shadow-lg"
              whileHover={{ scale: 1.1, rotate: 5 }}
            >
              {testimonial.author.charAt(0)}
            </motion.div>
            <div>
              <h4 className="text-white font-semibold">{testimonial.author}</h4>
              <p className="text-white/70 text-sm">{testimonial.role}</p>
              <Badge variant="glass" className="mt-1 text-xs">
                {testimonial.company}
              </Badge>
            </div>
          </motion.div>
        </CardContent>

        {/* Floating particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 rounded-full bg-white/30"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0, 1, 0],
                scale: [0, 1.5, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: i * 0.7,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </Card>
    </motion.div>
  );

  const swiperConfig = {
    cards: {
      effect: 'cards',
      grabCursor: true,
      cardsEffect: {
        perSlideOffset: 8,
        perSlideRotate: 2,
        rotate: true,
        slideShadows: true,
      },
    },
    coverflow: {
      effect: 'coverflow',
      grabCursor: true,
      centeredSlides: true,
      slidesPerView: 'auto',
      coverflowEffect: {
        rotate: 50,
        stretch: 0,
        depth: 100,
        modifier: 1,
        slideShadows: true,
      },
    }
  };

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      <Swiper
        ref={swiperRef}
        {...swiperConfig[effect]}
        pagination={{
          clickable: true,
          dynamicBullets: true,
        }}
        navigation={true}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        modules={[EffectCards, EffectCoverflow, Pagination, Navigation, Autoplay]}
        onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
        className="testimonials-swiper"
        style={{
          paddingBottom: '50px',
          height: effect === 'cards' ? '400px' : 'auto',
        }}
      >
        {testimonials.map((testimonial, index) => (
          <SwiperSlide 
            key={index} 
            style={effect === 'coverflow' ? { width: '350px' } : {}}
          >
            <TestimonialCard 
              testimonial={testimonial} 
              isActive={index === activeIndex}
            />
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom navigation */}
      <div className="flex justify-center items-center space-x-6 mt-8">
        <motion.button
          className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 group"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => swiperRef.current?.swiper.slidePrev()}
        >
          <motion.span
            className="text-xl"
            whileHover={{ x: -2 }}
          >
            ←
          </motion.span>
        </motion.button>

        <div className="text-white/70 text-sm font-medium">
          {activeIndex + 1} / {testimonials.length}
        </div>

        <motion.button
          className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 group"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => swiperRef.current?.swiper.slideNext()}
        >
          <motion.span
            className="text-xl"
            whileHover={{ x: 2 }}
          >
            →
          </motion.span>
        </motion.button>
      </div>
    </div>
  );
};

export default TestimonialsCarousel;
