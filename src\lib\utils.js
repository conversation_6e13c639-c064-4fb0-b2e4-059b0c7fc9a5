import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

// Animation variants for Framer Motion
export const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

export const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

export const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

export const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.5, ease: "easeOut" }
};

export const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export const staggerItem = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 }
};

// GSAP animation helpers
export const gsapFadeIn = (element, delay = 0) => {
  return {
    opacity: 0,
    y: 50,
    duration: 0.8,
    delay,
    ease: "power2.out"
  };
};

export const gsapSlideIn = (element, direction = "left", delay = 0) => {
  const x = direction === "left" ? -100 : direction === "right" ? 100 : 0;
  const y = direction === "up" ? -100 : direction === "down" ? 100 : 0;
  
  return {
    opacity: 0,
    x,
    y,
    duration: 0.8,
    delay,
    ease: "power2.out"
  };
};

// Color palette
export const colors = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  secondary: {
    50: '#fdf4ff',
    100: '#fae8ff',
    200: '#f5d0fe',
    300: '#f0abfc',
    400: '#e879f9',
    500: '#d946ef',
    600: '#c026d3',
    700: '#a21caf',
    800: '#86198f',
    900: '#701a75',
  },
  accent: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
  }
};

// Gradient combinations
export const gradients = {
  primary: 'bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600',
  secondary: 'bg-gradient-to-r from-purple-600 via-pink-600 to-red-600',
  accent: 'bg-gradient-to-r from-green-400 via-blue-500 to-purple-600',
  warm: 'bg-gradient-to-r from-orange-400 via-red-500 to-pink-600',
  cool: 'bg-gradient-to-r from-blue-400 via-cyan-500 to-teal-600',
  sunset: 'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600',
  ocean: 'bg-gradient-to-r from-blue-600 via-cyan-500 to-teal-400',
  forest: 'bg-gradient-to-r from-green-600 via-emerald-500 to-teal-400',
  cosmic: 'bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-900',
  aurora: 'bg-gradient-to-r from-green-300 via-blue-500 to-purple-600'
};

// Glass morphism styles
export const glassMorphism = {
  light: 'bg-white/10 backdrop-blur-md border border-white/20',
  medium: 'bg-white/20 backdrop-blur-lg border border-white/30',
  heavy: 'bg-white/30 backdrop-blur-xl border border-white/40',
  dark: 'bg-black/10 backdrop-blur-md border border-black/20'
};

// Shadow variations
export const shadows = {
  glow: 'shadow-2xl shadow-blue-500/25',
  colorful: 'shadow-2xl shadow-purple-500/25',
  warm: 'shadow-2xl shadow-orange-500/25',
  cool: 'shadow-2xl shadow-cyan-500/25'
};
