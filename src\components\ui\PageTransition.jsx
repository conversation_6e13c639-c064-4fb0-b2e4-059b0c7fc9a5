import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const pageVariants = {
  initial: {
    opacity: 0,
    y: 50,
    scale: 0.95,
  },
  in: {
    opacity: 1,
    y: 0,
    scale: 1,
  },
  out: {
    opacity: 0,
    y: -50,
    scale: 1.05,
  }
};

const pageTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.8
};

const slideVariants = {
  initial: {
    x: "100%",
  },
  in: {
    x: "0%",
  },
  out: {
    x: "-100%",
  }
};

const slideTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.6
};

// Curtain effect
const curtainVariants = {
  initial: {
    scaleY: 0,
    originY: 1,
  },
  animate: {
    scaleY: 1,
    originY: 1,
    transition: {
      duration: 0.6,
      ease: "easeInOut"
    }
  },
  exit: {
    scaleY: 0,
    originY: 0,
    transition: {
      duration: 0.6,
      ease: "easeInOut"
    }
  }
};

// Liquid transition
const liquidVariants = {
  initial: {
    clipPath: "circle(0% at 50% 50%)",
  },
  animate: {
    clipPath: "circle(150% at 50% 50%)",
    transition: {
      duration: 0.8,
      ease: "easeInOut"
    }
  },
  exit: {
    clipPath: "circle(0% at 50% 50%)",
    transition: {
      duration: 0.8,
      ease: "easeInOut"
    }
  }
};

// Morphing transition
const morphVariants = {
  initial: {
    borderRadius: "50%",
    scale: 0,
    rotate: 180,
  },
  animate: {
    borderRadius: "0%",
    scale: 1,
    rotate: 0,
    transition: {
      duration: 0.8,
      ease: "easeInOut"
    }
  },
  exit: {
    borderRadius: "50%",
    scale: 0,
    rotate: -180,
    transition: {
      duration: 0.8,
      ease: "easeInOut"
    }
  }
};

export const PageTransition = ({ 
  children, 
  type = "fade", 
  className = "",
  ...props 
}) => {
  const getVariants = () => {
    switch (type) {
      case "slide":
        return slideVariants;
      case "curtain":
        return curtainVariants;
      case "liquid":
        return liquidVariants;
      case "morph":
        return morphVariants;
      default:
        return pageVariants;
    }
  };

  const getTransition = () => {
    switch (type) {
      case "slide":
        return slideTransition;
      default:
        return pageTransition;
    }
  };

  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={getVariants()}
      transition={getTransition()}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Section reveal animation
export const SectionReveal = ({ 
  children, 
  direction = "up", 
  delay = 0,
  duration = 0.8,
  className = "",
  ...props 
}) => {
  const directionVariants = {
    up: {
      initial: { opacity: 0, y: 60 },
      animate: { opacity: 1, y: 0 }
    },
    down: {
      initial: { opacity: 0, y: -60 },
      animate: { opacity: 1, y: 0 }
    },
    left: {
      initial: { opacity: 0, x: -60 },
      animate: { opacity: 1, x: 0 }
    },
    right: {
      initial: { opacity: 0, x: 60 },
      animate: { opacity: 1, x: 0 }
    },
    scale: {
      initial: { opacity: 0, scale: 0.8 },
      animate: { opacity: 1, scale: 1 }
    },
    rotate: {
      initial: { opacity: 0, rotate: -10, scale: 0.9 },
      animate: { opacity: 1, rotate: 0, scale: 1 }
    }
  };

  return (
    <motion.div
      initial="initial"
      whileInView="animate"
      viewport={{ once: true, amount: 0.3 }}
      variants={directionVariants[direction]}
      transition={{
        duration,
        delay,
        ease: "easeOut"
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Stagger children animation
export const StaggerContainer = ({ 
  children, 
  staggerDelay = 0.1,
  className = "",
  ...props 
}) => {
  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      whileInView="animate"
      viewport={{ once: true, amount: 0.3 }}
      className={className}
      {...props}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

// Magnetic hover effect
export const MagneticHover = ({ 
  children, 
  strength = 0.3,
  className = "",
  ...props 
}) => {
  return (
    <motion.div
      whileHover={{
        scale: 1.05,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
      whileTap={{ scale: 0.95 }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;
