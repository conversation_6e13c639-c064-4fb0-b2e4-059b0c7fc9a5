import React from 'react';
import { motion } from 'framer-motion';

const CategorySection = () => {
  const categories = [
    {
      id: 'web',
      title: 'Web Development',
      description: 'Modern, responsive websites and web applications',
      projects: [
        {
          title: 'E-commerce Platform',
          description: 'Full-stack online store with payment integration and admin dashboard',
          tech: ['React', 'Node.js', 'MongoDB', 'Stripe']
        },
        {
          title: 'Corporate Website',
          description: 'Professional business website with CMS and SEO optimization',
          tech: ['Next.js', 'Tailwind CSS', 'Sanity CMS']
        },
        {
          title: 'Portfolio Website',
          description: 'Creative portfolio with animations and interactive elements',
          tech: ['React', 'Framer Motion', 'Three.js']
        },
        {
          title: 'SaaS Dashboard',
          description: 'Analytics dashboard with real-time data visualization',
          tech: ['React', 'D3.js', 'Firebase', 'Chart.js']
        },
        {
          title: 'Blog Platform',
          description: 'Content management system with user authentication',
          tech: ['Next.js', 'Prisma', 'PostgreSQL', 'NextAuth']
        },
        {
          title: 'Landing Page',
          description: 'High-converting landing page with A/B testing',
          tech: ['React', 'Tailwind CSS', 'Google Analytics']
        }
      ]
    },
    {
      id: 'app',
      title: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications',
      projects: [
        {
          title: 'Fitness Tracker App',
          description: 'Health monitoring app with workout plans and progress tracking',
          tech: ['React Native', 'Firebase', 'HealthKit', 'Redux']
        },
        {
          title: 'Food Delivery App',
          description: 'On-demand delivery platform with real-time tracking',
          tech: ['Flutter', 'Google Maps API', 'Stripe', 'Firebase']
        },
        {
          title: 'Social Media App',
          description: 'Photo sharing platform with messaging and stories',
          tech: ['React Native', 'AWS S3', 'Socket.io', 'MongoDB']
        },
        {
          title: 'Task Management App',
          description: 'Productivity app with team collaboration features',
          tech: ['Flutter', 'Supabase', 'Push Notifications']
        },
        {
          title: 'Learning Platform',
          description: 'Educational app with video streaming and quizzes',
          tech: ['React Native', 'Video.js', 'SQLite', 'Offline Sync']
        },
        {
          title: 'Finance Tracker',
          description: 'Personal finance management with budget planning',
          tech: ['Flutter', 'Plaid API', 'Chart.js', 'Biometric Auth']
        }
      ]
    },
    {
      id: 'aiml',
      title: 'AI/ML Solutions',
      description: 'Intelligent systems and machine learning applications',
      projects: [
        {
          title: 'AI Chatbot Tutor',
          description: 'Personalized learning assistant with natural language processing',
          tech: ['Python', 'OpenAI GPT', 'LangChain', 'Vector DB']
        },
        {
          title: 'Image Recognition System',
          description: 'Computer vision solution for automated quality control',
          tech: ['TensorFlow', 'OpenCV', 'Python', 'Docker']
        },
        {
          title: 'Recommendation Engine',
          description: 'ML-powered product recommendation system',
          tech: ['Scikit-learn', 'Pandas', 'Redis', 'FastAPI']
        },
        {
          title: 'Sentiment Analysis Tool',
          description: 'Real-time social media sentiment monitoring',
          tech: ['NLTK', 'Transformers', 'Streamlit', 'Twitter API']
        },
        {
          title: 'Predictive Analytics',
          description: 'Business intelligence dashboard with forecasting',
          tech: ['Prophet', 'Plotly', 'PostgreSQL', 'Apache Airflow']
        },
        {
          title: 'Voice Assistant',
          description: 'Custom voice AI for smart home automation',
          tech: ['Speech Recognition', 'TTS', 'IoT Integration', 'Edge AI']
        }
      ]
    },
    {
      id: 'devops',
      title: 'DevOps & Cloud',
      description: 'Infrastructure automation and cloud solutions',
      projects: [
        {
          title: 'CI/CD Pipeline',
          description: 'Automated deployment pipeline with testing and monitoring',
          tech: ['GitHub Actions', 'Docker', 'Kubernetes', 'AWS']
        },
        {
          title: 'Cloud Migration',
          description: 'Legacy system migration to cloud-native architecture',
          tech: ['AWS', 'Terraform', 'Microservices', 'API Gateway']
        },
        {
          title: 'Monitoring System',
          description: 'Comprehensive application and infrastructure monitoring',
          tech: ['Prometheus', 'Grafana', 'ELK Stack', 'AlertManager']
        },
        {
          title: 'Auto-scaling Solution',
          description: 'Dynamic resource allocation based on demand',
          tech: ['Kubernetes', 'HPA', 'VPA', 'Custom Metrics']
        },
        {
          title: 'Backup & Recovery',
          description: 'Automated backup system with disaster recovery',
          tech: ['Velero', 'AWS S3', 'Cross-region Replication']
        },
        {
          title: 'Security Hardening',
          description: 'Infrastructure security audit and implementation',
          tech: ['Security Policies', 'RBAC', 'Network Policies', 'Vault']
        }
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: 'easeOut'
      }
    }
  };

  const scrollToContact = () => {
    document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="py-20 bg-gray-50">
      {categories.map((category, categoryIndex) => (
        <motion.section
          key={category.id}
          id={category.id}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-20"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              {category.title}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {category.description}
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {category.projects.map((project, index) => (
              <motion.div
                key={index}
                variants={cardVariants}
                whileHover={{ 
                  y: -10,
                  boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
                }}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                onClick={scrollToContact}
              >
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
                  {project.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.tech.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full font-medium"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full py-2 bg-gradient-to-r from-primary-600 to-accent-600 text-white font-semibold rounded-lg hover:from-primary-700 hover:to-accent-700 transition-all duration-200"
                >
                  Learn More
                </motion.button>
              </motion.div>
            ))}
          </motion.div>
        </motion.section>
      ))}
    </div>
  );
};

export default CategorySection;
