import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { gsap } from 'gsap';
import ThreeBackground from './ui/ThreeBackground';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

const Hero = () => {
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);

  useEffect(() => {
    const handleMouseMove = (e) => {
      const rect = heroRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const mouseX = (e.clientX - centerX) / rect.width;
        const mouseY = (e.clientY - centerY) / rect.height;
        setMousePosition({ x: mouseX, y: mouseY });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  useEffect(() => {
    // GSAP animations
    const tl = gsap.timeline();
    
    tl.fromTo(titleRef.current, 
      { y: 100, opacity: 0, scale: 0.8 },
      { y: 0, opacity: 1, scale: 1, duration: 1.2, ease: "power3.out" }
    );
  }, []);

  const scrollToNext = () => {
    const nextSection = document.querySelector('#services');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <motion.section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{ y, opacity }}
    >
      {/* 3D Background */}
      <ThreeBackground />

      {/* Vibrant Animated Background Elements */}
      <div className="absolute inset-0 z-0">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              width: `${Math.random() * 400 + 150}px`,
              height: `${Math.random() * 400 + 150}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              background: `linear-gradient(135deg, ${
                ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#fd79a8', '#6c5ce7', '#a29bfe', '#00b894', '#e17055', '#0984e3', '#6c5ce7'][i]
              }, ${
                ['#ffeaa7', '#fd79a8', '#6c5ce7', '#a29bfe', '#00b894', '#e17055', '#0984e3', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#fd79a8'][i]
              })`,
              opacity: 0.4,
              filter: 'blur(2px)'
            }}
            animate={{
              scale: [1, 1.5, 1],
              rotate: [0, 360, 720],
              x: [0, Math.random() * 200 - 100, 0],
              y: [0, Math.random() * 200 - 100, 0],
              opacity: [0.4, 0.7, 0.4]
            }}
            transition={{
              duration: Math.random() * 15 + 10,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Vibrant Animated Background Gradients */}
      <div className="absolute inset-0 z-10">
        <motion.div
          className="absolute inset-0"
          style={{
            background: "linear-gradient(45deg, rgba(255, 107, 107, 0.3), rgba(78, 205, 196, 0.3), rgba(69, 183, 209, 0.3), rgba(150, 206, 180, 0.3))"
          }}
          animate={{
            background: [
              "linear-gradient(45deg, rgba(255, 107, 107, 0.3), rgba(78, 205, 196, 0.3), rgba(69, 183, 209, 0.3), rgba(150, 206, 180, 0.3))",
              "linear-gradient(45deg, rgba(255, 234, 167, 0.3), rgba(253, 121, 168, 0.3), rgba(108, 92, 231, 0.3), rgba(162, 155, 254, 0.3))",
              "linear-gradient(45deg, rgba(0, 184, 148, 0.3), rgba(225, 112, 85, 0.3), rgba(9, 132, 227, 0.3), rgba(108, 92, 231, 0.3))",
              "linear-gradient(45deg, rgba(255, 107, 107, 0.3), rgba(78, 205, 196, 0.3), rgba(69, 183, 209, 0.3), rgba(150, 206, 180, 0.3))"
            ]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
        />

        {/* Additional overlay for more depth */}
        <motion.div
          className="absolute inset-0"
          style={{
            background: "radial-gradient(circle at 30% 70%, rgba(255, 107, 107, 0.2) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(78, 205, 196, 0.2) 0%, transparent 50%)"
          }}
          animate={{
            background: [
              "radial-gradient(circle at 30% 70%, rgba(255, 107, 107, 0.2) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(78, 205, 196, 0.2) 0%, transparent 50%)",
              "radial-gradient(circle at 70% 30%, rgba(253, 121, 168, 0.2) 0%, transparent 50%), radial-gradient(circle at 30% 70%, rgba(108, 92, 231, 0.2) 0%, transparent 50%)",
              "radial-gradient(circle at 50% 50%, rgba(255, 234, 167, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(0, 184, 148, 0.2) 0%, transparent 50%)"
            ]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      {/* Colorful Floating Particles */}
      <div className="absolute inset-0 z-20">
        {[...Array(30)].map((_, i) => {
          const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#fd79a8', '#6c5ce7', '#a29bfe'];
          const shapes = ['rounded-full', 'rounded-lg', 'rounded-xl'];
          const sizes = ['w-2 h-2', 'w-3 h-3', 'w-4 h-4', 'w-1 h-1'];

          return (
            <motion.div
              key={i}
              className={`absolute ${sizes[i % sizes.length]} ${shapes[i % shapes.length]} opacity-80`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                background: colors[i % colors.length],
                boxShadow: `0 0 10px ${colors[i % colors.length]}40`
              }}
              animate={{
                y: [0, -150, 0],
                x: [0, Math.random() * 50 - 25, 0],
                opacity: [0, 1, 0],
                scale: [0, 1.2, 0],
                rotate: [0, 360, 720]
              }}
              transition={{
                duration: Math.random() * 4 + 3,
                repeat: Infinity,
                delay: Math.random() * 3,
                ease: "easeInOut"
              }}
            />
          );
        })}
      </div>

      {/* Main Content */}
      <div className="relative z-30 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          style={{ 
            x: mousePosition.x * 20,
            rotateX: mousePosition.y * 10,
            rotateY: mousePosition.x * 10
          }}
          className="space-y-8"
        >
          {/* Main Title */}
          <motion.div
            ref={titleRef}
            className="space-y-6"
          >
            <motion.div
              className="flex justify-center mb-6"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.1 }}
            >
              <Badge variant="gradient" className="text-lg px-6 py-2 animate-pulse-glow">
                ✨ Freshly Launched & Ready to Innovate
              </Badge>
            </motion.div>

            <motion.h1
              className="text-6xl sm:text-7xl lg:text-8xl xl:text-9xl font-black leading-tight"
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              <span className="block gradient-text animate-gradient-flow">
                Reimagining
              </span>
              <span className="block rainbow-text animate-rainbow-shift">
                Tech & Creativity
              </span>
            </motion.h1>

            <motion.div
              className="flex items-center justify-center space-x-4 text-2xl sm:text-3xl lg:text-4xl"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <motion.span
                className="neon-glow text-white"
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
              >
                ✨
              </motion.span>
              <span className="gradient-text font-bold">akrix.ai</span>
              <motion.span
                className="neon-glow text-white"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                🚀
              </motion.span>
            </motion.div>
          </motion.div>

          {/* Subtitle */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <p className="text-xl sm:text-2xl lg:text-3xl text-white font-medium max-w-4xl mx-auto leading-relaxed">
              <span className="typing-animation">
                Freshly launched, boldly creative
              </span>
            </p>
            
            <div className="glass-card max-w-5xl mx-auto p-8">
              <p className="text-lg sm:text-xl text-white leading-relaxed">
                We blend cutting-edge technology with artistic vision to deliver 
                exceptional web development, AI solutions, and creative services 
                that transform your ideas into reality.
              </p>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-8"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <motion.div
              whileHover={{ scale: 1.05, rotateX: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="gradient"
                size="xl"
                className="group relative overflow-hidden shadow-glow hover:shadow-glow-lg"
                onClick={(e) => {
                  e.preventDefault();
                  document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                <motion.span
                  className="flex items-center space-x-2"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                >
                  <span>🚀</span>
                  <span>Get Started Today</span>
                </motion.span>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, rotateX: -5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="glass"
                size="xl"
                className="group backdrop-blur-xl border-white/30 hover:border-white/50"
                asChild
              >
                <a
                  href="https://calendly.com/akrix-ai/akrix-freelancing-meeting"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span className="flex items-center space-x-2">
                    <span>📅</span>
                    <span>Schedule a Meeting</span>
                  </span>
                </a>
              </Button>
            </motion.div>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            className="pt-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1.8 }}
          >
            <motion.button
              onClick={scrollToNext}
              className="flex flex-col items-center space-y-2 text-white hover:text-blue-300 transition-colors group"
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <span className="text-sm font-medium">Scroll to explore</span>
              <motion.div
                className="w-6 h-10 border-2 border-white rounded-full flex justify-center"
                whileHover={{ borderColor: "rgba(255, 255, 255, 0.6)" }}
              >
                <motion.div
                  className="w-1 h-3 bg-white rounded-full mt-2"
                  animate={{ y: [0, 12, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                />
              </motion.div>
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default Hero;
