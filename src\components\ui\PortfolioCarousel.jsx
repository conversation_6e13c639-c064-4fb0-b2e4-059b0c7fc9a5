import React, { useRef, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectCreative, Pagination, Navigation, Autoplay, Thumbs, FreeMode } from 'swiper/modules';
import { motion } from 'framer-motion';
import { Card, CardContent } from './card';
import { Badge } from './badge';
import { Button } from './button';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/effect-creative';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import 'swiper/css/thumbs';
import 'swiper/css/free-mode';

const PortfolioCarousel = ({ projects }) => {
  const swiperRef = useRef(null);
  const thumbsRef = useRef(null);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const ProjectCard = ({ project, isActive }) => (
    <motion.div
      className="relative h-full group"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Card className="h-full bg-white/5 backdrop-blur-xl border-white/10 hover:border-white/30 transition-all duration-500 overflow-hidden">
        {/* Project Image */}
        <div className="relative h-64 overflow-hidden">
          <motion.img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          />
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Project Type Badge */}
          <div className="absolute top-4 left-4">
            <Badge variant="glass" className="text-xs">
              {project.type}
            </Badge>
          </div>
          
          {/* View Project Button */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100"
            initial={{ scale: 0.8 }}
            whileHover={{ scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Button
              variant="glass"
              size="lg"
              className="backdrop-blur-md"
              onClick={() => window.open(project.link, '_blank')}
            >
              <span className="flex items-center space-x-2">
                <span>👁️</span>
                <span>View Project</span>
              </span>
            </Button>
          </motion.div>
        </div>

        <CardContent className="p-6">
          {/* Project Title */}
          <motion.h3 
            className="text-xl font-bold text-white mb-2 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:from-purple-400 group-hover:to-pink-400 transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            {project.title}
          </motion.h3>
          
          {/* Project Description */}
          <motion.p 
            className="text-gray-300 text-sm leading-relaxed mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            {project.description}
          </motion.p>
          
          {/* Technologies */}
          <motion.div 
            className="flex flex-wrap gap-2 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            {project.technologies.map((tech, index) => (
              <Badge 
                key={index} 
                variant="outline" 
                className="text-xs border-white/20 text-white/80"
              >
                {tech}
              </Badge>
            ))}
          </motion.div>
          
          {/* Project Stats */}
          <motion.div 
            className="flex justify-between items-center text-sm text-gray-400"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <span>📅 {project.year}</span>
            <span>⭐ {project.rating}/5</span>
          </motion.div>
        </CardContent>

        {/* Floating particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 rounded-full bg-purple-400/40"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0, 1, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.5,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </Card>
    </motion.div>
  );

  return (
    <div className="relative w-full">
      {/* Main Carousel */}
      <Swiper
        ref={swiperRef}
        effect="creative"
        grabCursor={true}
        centeredSlides={true}
        slidesPerView={1}
        spaceBetween={30}
        creativeEffect={{
          prev: {
            shadow: true,
            translate: ["-20%", 0, -1],
            rotate: [0, 0, -10],
          },
          next: {
            translate: ["100%", 0, 0],
            rotate: [0, 0, 10],
          },
        }}
        thumbs={{ swiper: thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null }}
        pagination={{
          clickable: true,
          dynamicBullets: true,
        }}
        navigation={true}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        modules={[EffectCreative, Pagination, Navigation, Autoplay, Thumbs, FreeMode]}
        onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
        className="portfolio-swiper mb-8"
        style={{ paddingBottom: '50px' }}
        breakpoints={{
          640: {
            slidesPerView: 1.2,
          },
          768: {
            slidesPerView: 1.5,
          },
          1024: {
            slidesPerView: 2,
          },
        }}
      >
        {projects.map((project, index) => (
          <SwiperSlide key={index}>
            <ProjectCard 
              project={project} 
              isActive={index === activeIndex}
            />
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Thumbnails */}
      <Swiper
        ref={thumbsRef}
        onSwiper={setThumbsSwiper}
        spaceBetween={10}
        slidesPerView={4}
        freeMode={true}
        watchSlidesProgress={true}
        modules={[FreeMode, Thumbs]}
        className="portfolio-thumbs"
        breakpoints={{
          640: {
            slidesPerView: 5,
          },
          768: {
            slidesPerView: 6,
          },
          1024: {
            slidesPerView: 8,
          },
        }}
      >
        {projects.map((project, index) => (
          <SwiperSlide key={index}>
            <motion.div
              className={`relative h-16 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-300 ${
                index === activeIndex 
                  ? 'border-purple-400 shadow-lg shadow-purple-400/50' 
                  : 'border-white/20 hover:border-white/40'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <img
                src={project.image}
                alt={project.title}
                className="w-full h-full object-cover"
              />
              <div className={`absolute inset-0 bg-black/40 transition-opacity duration-300 ${
                index === activeIndex ? 'opacity-0' : 'opacity-60'
              }`} />
            </motion.div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Controls */}
      <div className="flex justify-center items-center space-x-6 mt-8">
        <motion.button
          className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => swiperRef.current?.swiper.slidePrev()}
        >
          ←
        </motion.button>

        <div className="text-white/70 text-sm font-medium">
          {activeIndex + 1} / {projects.length}
        </div>

        <motion.button
          className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => swiperRef.current?.swiper.slideNext()}
        >
          →
        </motion.button>
      </div>
    </div>
  );
};

export default PortfolioCarousel;
