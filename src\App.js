import React, { useEffect } from 'react';
import Lenis from '@studio-freight/lenis';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Header from './components/Header';
import Hero from './components/Hero';
import ServicesSection from './components/ServicesSection';
import AboutSection from './components/AboutSection';
import PricingSection from './components/PricingSection';
import CustomProjectsSection from './components/CustomProjectsSection';
import ContactSection from './components/ContactSection';
import FeedbackSection from './components/FeedbackSection';
import Footer from './components/Footer';
import { PageTransition } from './components/ui/PageTransition';
import { useScrollAnimations } from './hooks/useScrollAnimations';
import './App.css';

gsap.registerPlugin(ScrollTrigger);

function App() {
  // Initialize scroll animations
  useScrollAnimations();

  useEffect(() => {
    // Initialize enhanced Lenis for smooth scrolling
    const lenis = new Lenis({
      duration: 1.4,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 1.2,
      smoothTouch: false,
      touchMultiplier: 2,
      infinite: false,
      normalizeWheel: true,
      wheelMultiplier: 1.2,
    });

    // Enhanced RAF loop with GSAP integration
    function raf(time) {
      lenis.raf(time);
      ScrollTrigger.update();
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    // Sync Lenis with GSAP ScrollTrigger
    lenis.on('scroll', ScrollTrigger.update);

    // Add custom cursor effect
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    cursor.style.cssText = `
      position: fixed;
      width: 20px;
      height: 20px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      mix-blend-mode: difference;
      transition: transform 0.1s ease;
    `;
    document.body.appendChild(cursor);

    const updateCursor = (e) => {
      gsap.to(cursor, {
        x: e.clientX - 10,
        y: e.clientY - 10,
        duration: 0.1,
        ease: "power2.out"
      });
    };

    document.addEventListener('mousemove', updateCursor);

    return () => {
      lenis.destroy();
      document.removeEventListener('mousemove', updateCursor);
      document.body.removeChild(cursor);
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <PageTransition type="fade" className="App min-h-screen">
      <Header />
      <main className="pt-20">
        <Hero />
        <ServicesSection />
        <AboutSection />
        <PricingSection />
        <CustomProjectsSection />
        <ContactSection />
        <FeedbackSection />
        <Footer />
      </main>
    </PageTransition>
  );
}

export default App;
