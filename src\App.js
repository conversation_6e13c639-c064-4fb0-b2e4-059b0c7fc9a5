import React, { useEffect } from 'react';
import Lenis from '@studio-freight/lenis';
import Header from './components/Header';
import Hero from './components/Hero';
import ServicesSection from './components/ServicesSection';
import AboutSection from './components/AboutSection';
import PricingSection from './components/PricingSection';
import CustomProjectsSection from './components/CustomProjectsSection';
import ContactSection from './components/ContactSection';
import FeedbackSection from './components/FeedbackSection';
import Footer from './components/Footer';
import './App.css';

function App() {
  useEffect(() => {
    // Initialize Lenis for smooth scrolling
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 1,
      smoothTouch: false,
      touchMultiplier: 2,
      infinite: false,
    });

    function raf(time) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    return () => {
      lenis.destroy();
    };
  }, []);

  return (
    <div className="App min-h-screen">
      <Header />
      <main className="pt-20">
        <Hero />
        <ServicesSection />
        <AboutSection />
        <PricingSection />
        <CustomProjectsSection />
        <ContactSection />
        <FeedbackSection />
        <Footer />
      </main>
    </div>
  );
}

export default App;
